@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply text-neutral-900 bg-neutral-50;
  }

  * {
    @apply border-neutral-200;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold text-neutral-900;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }

  h5 {
    @apply text-lg lg:text-xl;
  }

  h6 {
    @apply text-base lg:text-lg;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm rounded-lg;
  }

  .btn-md {
    @apply px-4 py-2 text-sm rounded-lg;
  }

  .btn-lg {
    @apply px-6 py-3 text-base rounded-xl;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg rounded-xl;
  }

  .btn-primary {
    @apply btn bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white shadow-sm hover:shadow-md focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-white hover:bg-neutral-50 active:bg-neutral-100 text-neutral-700 border border-neutral-300 shadow-sm hover:shadow-md focus:ring-primary-500;
  }

  .btn-accent {
    @apply btn bg-accent-600 hover:bg-accent-700 active:bg-accent-800 text-white shadow-sm hover:shadow-md focus:ring-accent-500;
  }

  .btn-ghost {
    @apply btn bg-transparent hover:bg-neutral-100 active:bg-neutral-200 text-neutral-700 focus:ring-primary-500;
  }

  .btn-danger {
    @apply btn bg-error-600 hover:bg-error-700 active:bg-error-800 text-white shadow-sm hover:shadow-md focus:ring-error-500;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-neutral-200 overflow-hidden;
  }

  .card-body {
    @apply p-6;
  }

  .card-header {
    @apply px-6 py-4 border-b border-neutral-200 bg-neutral-50;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-neutral-200 bg-neutral-50;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-medium hover:-translate-y-1;
  }

  /* Form Components */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-neutral-700;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-neutral-300 rounded-lg shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-textarea {
    @apply form-input resize-vertical min-h-[100px];
  }

  .form-select {
    @apply form-input pr-10 bg-white;
  }

  .form-error {
    @apply border-error-300 focus:border-error-500 focus:ring-error-500;
  }

  .form-success {
    @apply border-success-300 focus:border-success-500 focus:ring-success-500;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-lg border;
  }

  .alert-info {
    @apply alert bg-primary-50 border-primary-200 text-primary-800;
  }

  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }

  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }

  .alert-error {
    @apply alert bg-error-50 border-error-200 text-error-800;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply badge bg-neutral-100 text-neutral-800;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }

  /* Navigation Components */
  .nav-link {
    @apply flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }

  .nav-link-inactive {
    @apply nav-link text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100;
  }

  /* Table Components */
  .table {
    @apply w-full divide-y divide-neutral-200;
  }

  .table-header {
    @apply bg-neutral-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-neutral-200;
  }

  .table-row {
    @apply hover:bg-neutral-50 transition-colors duration-150;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-neutral-900;
  }

  /* Utility Classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-700;
  }

  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-700;
  }

  .bg-gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-700;
  }

  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .glass-dark {
    @apply bg-neutral-900/80 backdrop-blur-sm border border-neutral-700/20;
  }
}