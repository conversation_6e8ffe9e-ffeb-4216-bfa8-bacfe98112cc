import { supabase } from './supabase'
import { User } from '@/types'

export const AdminUserManager = {
  getAllUsers: async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      return { success: true, data: data || [] }
    } catch (error) {
      console.error('Error fetching users:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch users',
        data: []
      }
    }
  },

  updateUserRole: async (userId: string, newRole: User['role']) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          role: newRole,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error

      return { success: true, data }
    } catch (error) {
      console.error('Error updating user role:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user role'
      }
    }
  },

  deleteUser: async (userId: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('Error deleting user:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete user'
      }
    }
  },

  createUser: async (userData: {
    email: string
    name: string
    role: User['role']
    password: string
  }) => {
    try {
      // First create the auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
            role: userData.role
          }
        }
      })

      if (authError) throw authError

      if (!authData.user) {
        throw new Error('Failed to create user account')
      }

      // Then create the user profile
      const { data, error } = await supabase
        .from('users')
        .insert([{
          id: authData.user.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (error) throw error

      return { success: true, data }
    } catch (error) {
      console.error('Error creating user:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create user'
      }
    }
  }
}

export const canManageRole = (currentUserRole: User['role'], targetRole: User['role']): boolean => {
  // Role hierarchy: admin > editor > viewer
  const roleHierarchy = {
    admin: 3,
    editor: 2,
    viewer: 1
  }

  const currentLevel = roleHierarchy[currentUserRole] || 0
  const targetLevel = roleHierarchy[targetRole] || 0

  // Admins can manage all roles
  if (currentUserRole === 'admin') {
    return true
  }

  // Editors can only manage viewers
  if (currentUserRole === 'editor' && targetRole === 'viewer') {
    return true
  }

  // Users can't manage roles equal or higher than their own
  return currentLevel > targetLevel
}

export const getAssignableRoles = (currentUserRole: User['role']): User['role'][] => {
  switch (currentUserRole) {
    case 'admin':
      return ['admin', 'editor', 'viewer']
    case 'editor':
      return ['viewer']
    case 'viewer':
    default:
      return []
  }
}

export const getRoleDescription = (role: User['role']): string => {
  switch (role) {
    case 'admin':
      return 'Full access to all features and user management'
    case 'editor':
      return 'Can create and edit content, manage categories and tags'
    case 'viewer':
      return 'Read-only access to content'
    default:
      return 'Unknown role'
  }
}