'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams, useRouter } from 'next/navigation'
import Image from 'next/image'
import { supabase } from '@/lib/supabase'
import { Post, Category, User as PostAuthor } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { ArrowLeft, Edit, Eye, Calendar, User, Tag, CheckCircle, XCircle, Archive } from 'lucide-react'
import Link from 'next/link'

type PostDetail = Post & {
  author: PostAuthor | null
  category: Category | null
}

export default function PostViewPage() {
  const { user, canEdit } = useAuthContext()
  const params = useParams()
  const router = useRouter()
  const { id } = params

  const [post, setPost] = useState<PostDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPost = useCallback(async () => {
    if (!id) return
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          author:users(name, email),
          category:categories(name, slug),
          featured_image_url
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      setPost(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post')
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    fetchPost()
  }, [fetchPost])

  const updateStatus = async (newStatus: 'published' | 'draft' | 'archived') => {
    if (!post) return

    try {
      const { error } = await supabase
        .from('posts')
        .update({ status: newStatus, updated_at: new Date().toISOString() })
        .eq('id', post.id)

      if (error) throw error
      
      setPost({ ...post, status: newStatus })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update status')
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading post...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center text-red-600">
          <p>{error}</p>
          <Link href="/admin/posts" className="mt-4 inline-block text-primary-600 hover:underline">
            Back to posts
          </Link>
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Post not found.</p>
          <Link href="/admin/posts" className="mt-4 inline-block text-primary-600 hover:underline">
            Back to posts
          </Link>
        </div>
      </div>
    )
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor', 'viewer']}>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div>
          <Link href="/admin/posts" className="flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Posts
          </Link>
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900">{post.title}</h1>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1.5" />
                  <span>{new Date(post.created_at).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-1.5" />
                  <span>{post.author?.name || post.author?.email || 'Unknown'}</span>
                </div>
                <div className="flex items-center">
                  <Tag className="w-4 h-4 mr-1.5" />
                  <span>{post.category?.name || 'Uncategorized'}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Link href={`/blog/${post.slug}`} target="_blank" className="p-2 rounded-md hover:bg-gray-100" title="View live post">
                <Eye className="w-5 h-5 text-gray-500" />
              </Link>
              {(canEdit() || post.author_id === user?.id) && (
                <Link href={`/admin/posts/${post.id}/edit`} className="p-2 rounded-md hover:bg-gray-100" title="Edit post">
                  <Edit className="w-5 h-5 text-primary-600" />
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Status and Actions */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-700 mr-3">Status:</span>
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusBadgeColor(post.status)}`}>
              {post.status}
            </span>
          </div>
          {canEdit() && (
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => updateStatus('published')} 
                disabled={post.status === 'published'}
                className="flex items-center px-3 py-1.5 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-green-300 disabled:cursor-not-allowed"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Publish
              </button>
              <button 
                onClick={() => updateStatus('draft')} 
                disabled={post.status === 'draft'}
                className="flex items-center px-3 py-1.5 text-sm bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-yellow-300 disabled:cursor-not-allowed"
              >
                <Archive className="w-4 h-4 mr-2" />
                Draft
              </button>
              <button 
                onClick={() => updateStatus('archived')} 
                disabled={post.status === 'archived'}
                className="flex items-center px-3 py-1.5 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Archive
              </button>
            </div>
          )}
        </div>

        {/* Post Content */}
        <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
          {post.featured_image && (
            <div className="relative w-full h-64 mb-8 rounded-lg overflow-hidden">
              <Image
                src={post.featured_image}
                alt={post.title}
                className="object-cover rounded-lg"
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              />
            </div>
          )}
          <div
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>
      </div>
    </ProtectedRoute>
  )
}