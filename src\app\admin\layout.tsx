'use client'

import { useState, useMemo, useCallback } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import {
  LayoutDashboard,
  FileText,
  HelpCircle,
  Upload,
  Settings,
  Users,
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Sparkles,
  Bell,
  Search
} from 'lucide-react'
import { useAuthContext } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { cn } from '@/lib/utils'

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  requiredRole?: 'admin' | 'editor'
  children?: NavItem[]
}

const navigation: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard
  },
  {
    name: 'Blog Posts',
    href: '/admin/posts',
    icon: FileText,
    children: [
      { name: 'All Posts', href: '/admin/posts', icon: FileText },
      { name: 'Create Post', href: '/admin/posts/create', icon: FileText },
      { name: 'Categories', href: '/admin/categories', icon: FileText },
      { name: 'Tags', href: '/admin/tags', icon: FileText }
    ]
  },
  {
    name: 'Help Articles',
    href: '/admin/help',
    icon: HelpCircle,
    children: [
      { name: 'All Articles', href: '/admin/help', icon: HelpCircle },
      { name: 'Create Article', href: '/admin/help/create', icon: HelpCircle }
    ]
  },
  {
    name: 'Media Files',
    href: '/admin/media',
    icon: Upload
  },
  {
    name: 'Users',
    href: '/admin/users',
    icon: Users,
    requiredRole: 'admin'
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    requiredRole: 'admin'
  }
]

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const { user, signOut, hasRole } = useAuthContext()
  const pathname = usePathname()
  const router = useRouter()

  const handleSignOut = useCallback(async () => {
    const result = await signOut()
    if (result.success) {
      router.push('/')
    }
  }, [signOut, router])

  const toggleExpanded = useCallback((itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }, [])

  const isActive = useCallback((href: string) => {
    if (href === '/admin') {
      return pathname === '/admin'
    }
    return pathname.startsWith(href)
  }, [pathname])

  const filteredNavigation = useMemo(() => navigation.filter(item =>
    !item.requiredRole || hasRole(item.requiredRole)
  ), [hasRole])

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="min-h-screen bg-neutral-50">

        {/* Mobile sidebar backdrop */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-neutral-900/50 backdrop-blur-sm lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={cn(
          "fixed inset-y-0 left-0 z-50 w-72 bg-white shadow-large border-r border-neutral-200 transform transition-transform duration-300 ease-in-out flex flex-col",
          sidebarOpen ? "translate-x-0" : "-translate-x-full",
          "lg:translate-x-0"
        )}>
          {/* Sidebar Header */}
          <div className="flex items-center justify-between h-20 px-6 border-b border-neutral-200 bg-gradient-to-r from-primary-600 to-secondary-600 flex-shrink-0">
            <Link href="/admin" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-primary-600" />
              </div>
              <span className="text-xl font-bold text-white">Eria CMS</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden text-white hover:text-neutral-200 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-6 px-4">
            <div className="space-y-1">
              {filteredNavigation.map((item) => (
                <div key={item.name}>
                  {item.children ? (
                    <div>
                      <button
                        onClick={() => toggleExpanded(item.name)}
                        className={cn(
                          "w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group",
                          isActive(item.href)
                            ? "bg-primary-50 text-primary-700 shadow-sm"
                            : "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900"
                        )}
                      >
                        <div className="flex items-center">
                          <item.icon className={cn(
                            "w-5 h-5 mr-3 transition-colors",
                            isActive(item.href) ? "text-primary-600" : "text-neutral-500 group-hover:text-neutral-700"
                          )} />
                          {item.name}
                        </div>
                        <ChevronDown className={cn(
                          "w-4 h-4 transition-transform duration-200",
                          expandedItems.includes(item.name) && "rotate-180"
                        )} />
                      </button>
                      {expandedItems.includes(item.name) && (
                        <div className="ml-4 mt-2 space-y-1 border-l-2 border-neutral-100 pl-4">
                          {item.children.map((child) => (
                            <Link
                              key={child.href}
                              href={child.href}
                              className={cn(
                                "flex items-center px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group",
                                isActive(child.href)
                                  ? "bg-primary-50 text-primary-700 font-medium"
                                  : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900"
                              )}
                              onClick={() => setSidebarOpen(false)}
                            >
                              <ChevronRight className={cn(
                                "w-4 h-4 mr-2 transition-colors",
                                isActive(child.href) ? "text-primary-600" : "text-neutral-400 group-hover:text-neutral-600"
                              )} />
                              {child.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group",
                        isActive(item.href)
                          ? "bg-primary-50 text-primary-700 shadow-sm"
                          : "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900"
                      )}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <item.icon className={cn(
                        "w-5 h-5 mr-3 transition-colors",
                        isActive(item.href) ? "text-primary-600" : "text-neutral-500 group-hover:text-neutral-700"
                      )} />
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
            </div>
          </nav>

          {/* User info and logout */}
          <div className="flex-shrink-0 p-4 border-t border-neutral-200 bg-neutral-50">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-sm">
                <span className="text-sm font-bold text-white">
                  {user?.name?.charAt(0) || user?.email?.charAt(0)}
                </span>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-semibold text-neutral-900 truncate">
                  {user?.name || 'User'}
                </p>
                <div className="flex items-center space-x-2">
                  <Badge variant="primary" size="sm">
                    {user?.role}
                  </Badge>
                </div>
              </div>
            </div>
            <Button
              onClick={handleSignOut}
              variant="ghost"
              size="sm"
              className="w-full justify-start text-neutral-700 hover:text-error-700 hover:bg-error-50"
              leftIcon={<LogOut className="w-4 h-4" />}
            >
              Sign Out
            </Button>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col bg-neutral-50 min-h-screen overflow-y-auto lg:ml-72">

          {/* Top bar */}
          <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-sm shadow-soft border-b border-neutral-200 flex-shrink-0">
            <div className="flex items-center justify-between h-20 px-6 sm:px-8">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-2 text-neutral-500 hover:text-neutral-700 hover:bg-neutral-100 rounded-lg transition-colors"
                >
                  <Menu className="w-5 h-5" />
                </button>

                {/* Search Bar */}
                <div className="hidden md:flex items-center bg-neutral-100 rounded-lg px-3 py-2 w-64">
                  <Search className="w-4 h-4 text-neutral-400 mr-2" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="bg-transparent text-sm text-neutral-700 placeholder-neutral-400 focus:outline-none flex-1"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-3">
                {/* Notifications */}
                <button className="relative p-2 text-neutral-500 hover:text-neutral-700 hover:bg-neutral-100 rounded-lg transition-colors">
                  <Bell className="w-5 h-5" />
                  <span className="absolute top-1 right-1 w-2 h-2 bg-error-500 rounded-full"></span>
                </button>

                {/* User Menu */}
                <div className="flex items-center space-x-3">
                  <div className="hidden sm:block text-right">
                    <p className="text-sm font-medium text-neutral-900">
                      {user?.name || 'User'}
                    </p>
                    <p className="text-xs text-neutral-500 capitalize">
                      {user?.role}
                    </p>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                    <span className="text-sm font-bold text-white">
                      {user?.name?.charAt(0) || user?.email?.charAt(0)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1 p-6 sm:p-8">
            {children}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}