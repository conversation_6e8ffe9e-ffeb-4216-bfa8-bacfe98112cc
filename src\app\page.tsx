import Link from 'next/link'
import { ArrowRight, BookOpen, FileText, Upload, Zap, Shield, Users, Sparkles, ChevronRight, Star } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardBody } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Navigation */}
      <nav className="relative z-10 bg-white/80 backdrop-blur-sm border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gradient">Eria CMS</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/blog">
                <Button variant="ghost" size="sm">Blog</Button>
              </Link>
              <Link href="/help">
                <Button variant="ghost" size="sm">Help</Button>
              </Link>
              <Link href="/login">
                <Button variant="secondary" size="sm">Sign In</Button>
              </Link>
              <Link href="/admin">
                <Button variant="primary" size="sm">Admin Panel</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden pt-20 pb-32">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-secondary-600/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Badge variant="primary" className="mb-6">
              <Sparkles className="w-3 h-3 mr-1" />
              Modern CMS Platform
            </Badge>
            <h1 className="text-5xl md:text-7xl font-bold text-neutral-900 mb-6 leading-tight">
              Internal Content
              <span className="block text-gradient">Management System</span>
            </h1>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-10 leading-relaxed">
              Manage your organization's content, documentation, and resources with our
              comprehensive internal CMS platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/admin">
                <Button size="xl" rightIcon={<ArrowRight className="w-5 h-5" />}>
                  Get Started
                </Button>
              </Link>
              <Link href="/blog">
                <Button variant="secondary" size="xl" rightIcon={<ChevronRight className="w-5 h-5" />}>
                  View Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-neutral-900 mb-4">
              Everything you need to manage content
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Powerful features designed to make content management effortless and enjoyable.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card hover padding="lg" className="text-center group">
              <CardBody>
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                  <FileText className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-neutral-900 mb-4">Blog Management</h3>
                <p className="text-neutral-600 leading-relaxed">
                  Create stunning blog posts with our rich text editor. Organize content with categories,
                  tags, and advanced publishing controls.
                </p>
              </CardBody>
            </Card>

            <Card hover padding="lg" className="text-center group">
              <CardBody>
                <div className="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-neutral-900 mb-4">Help Center</h3>
                <p className="text-neutral-600 leading-relaxed">
                  Build comprehensive documentation and help articles. Create searchable knowledge
                  bases that your users will love.
                </p>
              </CardBody>
            </Card>

            <Card hover padding="lg" className="text-center group">
              <CardBody>
                <div className="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                  <Upload className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-neutral-900 mb-4">Media Management</h3>
                <p className="text-neutral-600 leading-relaxed">
                  Upload, organize, and manage all your media files. Support for images, documents,
                  and downloadable resources.
                </p>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>

      {/* Additional Features */}
      <section className="py-24 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold text-neutral-900 mb-6">
                Built for modern teams
              </h2>
              <p className="text-xl text-neutral-600 mb-8">
                Collaborate seamlessly with role-based permissions, real-time editing,
                and powerful workflow management.
              </p>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Shield className="w-4 h-4 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-1">Secure & Reliable</h3>
                    <p className="text-neutral-600">Enterprise-grade security with role-based access control.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Zap className="w-4 h-4 text-accent-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-1">Lightning Fast</h3>
                    <p className="text-neutral-600">Optimized for speed with modern web technologies.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Users className="w-4 h-4 text-secondary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-1">Team Collaboration</h3>
                    <p className="text-neutral-600">Work together with advanced user management and permissions.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl transform rotate-3"></div>
              <Card className="relative bg-white rounded-3xl p-8 transform -rotate-1">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <Star className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-neutral-900">Dashboard Overview</h4>
                      <p className="text-sm text-neutral-600">Real-time analytics and insights</p>
                    </div>
                  </div>
                  <div className="h-2 bg-neutral-100 rounded-full overflow-hidden">
                    <div className="h-full w-3/4 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-neutral-900">150+</div>
                      <div className="text-sm text-neutral-600">Posts</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-neutral-900">25</div>
                      <div className="text-sm text-neutral-600">Articles</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-neutral-900">500+</div>
                      <div className="text-sm text-neutral-600">Files</div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-primary-600 to-secondary-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to get started?
          </h2>
          <p className="text-xl text-primary-100 mb-10">
            Streamline your organization's content management and documentation workflow.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/admin">
              <Button
                size="xl"
                className="bg-white text-primary-600 hover:bg-neutral-50"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                Access Admin Panel
              </Button>
            </Link>
            <Link href="/login">
              <Button
                variant="ghost"
                size="xl"
                className="text-white border-white hover:bg-white/10"
                rightIcon={<ChevronRight className="w-5 h-5" />}
              >
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 text-neutral-300 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">Eria CMS</h3>
            <p className="text-neutral-400 mb-6">
              Modern content management for the modern web.
            </p>
            <div className="flex justify-center space-x-6">
              <Link href="/blog" className="hover:text-white transition-colors">Blog</Link>
              <Link href="/help" className="hover:text-white transition-colors">Help</Link>
              <Link href="/admin" className="hover:text-white transition-colors">Dashboard</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}