'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowLeft, Calendar, User, Tag, Eye, Home, ChevronRight, Clock, Share2 } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { Post } from '@/types'
import { formatDate } from '@/lib/utils'
import { Card, CardBody } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'

export default function BlogPostPage() {
  const params = useParams()
  const slug = params.slug as string
  const [post, setPost] = useState<Post | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPost = useCallback(async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          author:users(name, email),
          category:categories(name, slug),
          tags:post_tags(tag:tags(name, slug))
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single()

      if (error) throw error
      setPost(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Post not found')
    } finally {
      setLoading(false)
    }
  }, [slug])

  useEffect(() => {
    if (slug) {
      fetchPost()
    }
  }, [slug, fetchPost])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading post...</p>
        </div>
      </div>
    )
  }

  if (error || !post) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Eye className="w-8 h-8 text-error-600" />
          </div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-4">Post Not Found</h1>
          <p className="text-neutral-600 mb-8 max-w-md mx-auto">
            {error || 'The requested post could not be found or may have been removed.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/blog">
              <Button leftIcon={<ArrowLeft className="w-4 h-4" />}>
                Back to Blog
              </Button>
            </Link>
            <Link href="/">
              <Button variant="secondary">
                Go Home
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Navigation Breadcrumb */}
      <div className="bg-white border-b border-neutral-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-neutral-500 hover:text-neutral-700 transition-colors">
              <Home className="w-4 h-4" />
            </Link>
            <ChevronRight className="w-4 h-4 text-neutral-400" />
            <Link href="/blog" className="text-neutral-500 hover:text-neutral-700 transition-colors">
              Blog
            </Link>
            <ChevronRight className="w-4 h-4 text-neutral-400" />
            <span className="text-neutral-900 font-medium truncate">{post.title}</span>
          </nav>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Post Header */}
        <article>
          {post.featured_image && (
            <div className="aspect-video w-full relative rounded-2xl overflow-hidden mb-8 shadow-large">
              <Image
                src={post.featured_image}
                alt={post.title}
                className="object-cover"
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          )}

          <Card className="overflow-hidden">
            <CardBody className="p-8 lg:p-12">
              {/* Category and Meta */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  {post.category && (
                    <Badge variant="primary" size="lg">
                      {post.category.name}
                    </Badge>
                  )}
                  <div className="flex items-center text-sm text-neutral-500">
                    <Clock className="w-4 h-4 mr-1" />
                    <time dateTime={post.published_at || post.created_at}>
                      {formatDate(post.published_at || post.created_at)}
                    </time>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="text-neutral-500">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>

              {/* Title */}
              <h1 className="text-4xl lg:text-5xl font-bold text-neutral-900 mb-8 leading-tight">
                {post.title}
              </h1>

              {/* Author Information */}
              <div className="flex items-center space-x-4 mb-8 pb-8 border-b border-neutral-200">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-neutral-900">
                    {post.author?.name || post.author?.email || 'Anonymous'}
                  </p>
                  <p className="text-sm text-neutral-500">
                    Published on {formatDate(post.published_at || post.created_at)}
                  </p>
                </div>
              </div>

              {/* Excerpt */}
              {post.excerpt && (
                <div className="text-xl text-neutral-600 mb-10 leading-relaxed font-light border-l-4 border-primary-500 pl-6 italic">
                  {post.excerpt}
                </div>
              )}

              {/* Content */}
              <div className="prose prose-lg prose-neutral max-w-none">
                <div className="whitespace-pre-wrap text-neutral-800 leading-relaxed">
                  {post.content}
                </div>
              </div>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="mt-12 pt-8 border-t border-neutral-200">
                  <div className="flex items-center flex-wrap gap-3">
                    <div className="flex items-center text-neutral-600 mr-2">
                      <Tag className="w-4 h-4 mr-2" />
                      <span className="text-sm font-medium">Tags:</span>
                    </div>
                    {post.tags.map((tagRelation: any) => (
                      <Badge
                        key={tagRelation.tag.slug}
                        variant="secondary"
                        size="sm"
                      >
                        {tagRelation.tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </article>

        {/* Navigation */}
        <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-between items-center">
          <Link href="/blog">
            <Button variant="secondary" leftIcon={<ArrowLeft className="w-4 h-4" />}>
              Back to Blog
            </Button>
          </Link>
          <Link href="/">
            <Button variant="ghost">
              Go Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}